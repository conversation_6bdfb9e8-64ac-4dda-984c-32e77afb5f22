{"author": "Adebola A<PERSON>lafe", "repository": {"type": "git", "url": "git+https://github.com/adebola-io/retend.git"}, "bugs": {"url": "https://github.com/adebola-io/retend/issues"}, "description": "A modern reactive framework for fluid, dynamic web apps.", "homepage": "https://github.com/adebola-io/retend#readme", "license": "MIT", "private": true, "scripts": {"test": "cd ./tests && bun test", "build": "bun build-core && bun build-ssg && bun build-utils", "build-core": "cd packages/retend && bun run build", "build-ssg": "cd packages/retend-server && bun run build", "build-utils": "cd packages/retend-utils && bun run build", "publish-core": "cd packages/retend && bun run build && bun publish", "publish-ssg": "cd packages/retend-server && bun run build && bun publish", "publish-start": "cd packages/retend-start && bun publish", "publish-utils": "cd packages/retend-utils && bun run build && bun publish", "publish-all": "bun run publish-core && bun run publish-ssg && bun run publish-start && bun run publish-utils", "pack-all": "chmod +x ./scripts/pack.sh && ./scripts/pack.sh", "previews": "cp README.md packages/retend/README.md && bunx pkg-pr-new publish './packages/retend' './packages/retend-server' './packages/retend-start' './packages/retend-utils'"}, "workspaces": ["packages/retend", "packages/retend-server", "packages/retend-start", "packages/retend-utils", "examples/utils-testing", "tests"]}