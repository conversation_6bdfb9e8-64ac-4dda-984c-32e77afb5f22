{"name": "retend", "version": "0.0.13", "description": "A modern reactive framework for fluid, dynamic web apps.", "private": false, "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "bun scripts/build.js"}, "keywords": ["web", "javascript", "pwa", "jsx"], "exports": {".": {"import": "./dist/library/index.js", "types": "./dist/library/index.d.ts"}, "./router": {"import": "./dist/router/index.js", "types": "./dist/router/index.d.ts"}, "./teleport": {"import": "./dist/teleport/index.js", "types": "./dist/teleport/index.d.ts"}, "./shadowroot": {"import": "./dist/shadowroot/index.js", "types": "./dist/shadowroot/index.d.ts"}, "./jsx-runtime": {"import": "./dist/jsx-runtime/index.js", "types": "./dist/jsx-runtime/index.d.ts"}, "./jsx-dev-runtime": {"import": "./dist/jsx-runtime/index.js", "types": "./dist/jsx-runtime/index.d.ts"}, "./helpers": {"import": "./dist/helpers/index.js", "types": "./dist/helpers/index.d.ts"}, "./render": {"import": "./dist/render/index.js", "types": "./dist/render/index.d.ts"}, "./include": {"import": "./dist/include/index.js", "types": "./dist/include/index.d.ts"}, "./v-dom": {"import": "./dist/v-dom/index.js", "types": "./dist/v-dom/index.d.ts"}, "./plugin": {"import": "./dist/plugin/index.js", "types": "./dist/plugin/index.d.ts"}, "./plugin/hmr": {"import": "./dist/plugin/hmr.js", "types": "./dist/plugin/hmr.d.ts"}, "./context": {"import": "./dist/context/index.js", "types": "./dist/context/index.d.ts"}}, "homepage": "https://github.com/adebola-io/retend#readme", "bugs": {"url": "https://github.com/adebola-io/retend/issues"}, "author": "Adebola A<PERSON>lafe", "repository": {"directory": "github.com/adebola-io/retend", "type": "git"}, "license": "MIT", "devDependencies": {"typescript": "^5.8.2"}, "dependencies": {"@adbl/cells": "^0.0.14", "csstype": "^3.1.3"}, "publishConfig": {"access": "public"}}