{"name": "retend-start", "version": "0.0.13", "private": false, "type": "module", "description": "Scaffold a new project with retend", "main": "index.js", "scripts": {"start": "node index.js"}, "bin": {"scaffold": "index.js"}, "repository": {"type": "git", "url": "git+https://github.com/adebola-io/retend.git"}, "keywords": ["web", "javascript", "jsx", "reactivity"], "author": "Adebola A<PERSON>lafe", "license": "MIT", "bugs": {"url": "https://github.com/adebola-io/retend/issues"}, "homepage": "https://github.com/adebola-io/retend#readme", "dependencies": {"@types/node": "^22.7.5", "@types/semver": "^7.7.0", "chalk": "^5.3.0", "inquirer": "^12.0.0", "ora": "^8.1.0", "semver": "^7.7.1"}, "publishConfig": {"access": "public"}}