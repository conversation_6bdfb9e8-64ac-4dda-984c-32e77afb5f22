{"name": "retend-server", "version": "0.0.13", "description": "Static site generation for the retend UI framework.", "private": false, "type": "module", "scripts": {"build": "bun scripts/build.js"}, "dependencies": {"acorn": "^8.14.1", "domhandler": "^5.0.3", "estree-walker": "^3.0.3", "htmlparser2": "^10.0.0", "magic-string": "^0.30.17", "oxc-walker": "^0.2.5"}, "exports": {"./server": {"import": "./dist/server.js", "types": "./dist/server.d.ts"}, "./client": {"import": "./dist/client.js", "types": "./dist/client.d.ts"}, "./plugin": {"import": "./dist/plugin.js", "types": "./dist/plugin.d.ts"}, "./types": {"import": "./dist/types.js", "types": "./dist/types.d.ts"}}, "devDependencies": {"typescript": "^5.8.2", "vite": "^6.2.1"}, "peerDependencies": {"retend": "*"}, "publishConfig": {"access": "public"}}